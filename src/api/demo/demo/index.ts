import alovaInstance from '@/api/index'
import type { ResponseListModel } from '@/types/core/data-model'
import type { QueryParams } from '@/types/api/queryParams'

import type { Demo, DemoCreate, DemoUpdate } from './types'

/**
 * @description 获取Demo元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getDemoMetadata = () => {
  return alovaInstance.Get<any>('/v1/demo/demo/get_metadata')
}

/**
 * @description 获取Demo列表
 * @param {DemoQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Demo>>} 返回包含Demo信息的Promise对象
 * @example
 * // 使用示例
 * const demoList = await getDemoList({ start: 1, limit: 20 });
 */
const getDemoList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<Demo>>(
    '/v1/demo/demo/query',
    params
  )
}

/**
 * @description 获取Demo详情
 * @param {number} id Demo ID
 * @returns {Promise<Demo>} 返回Demo详情信息
 */
const getDemo = (id: number, max_depth: number = 1) => {
  return alovaInstance.Get<Demo>('/v1/demo/demo/get', {
    params: {
      id: id,
      max_depth: max_depth,
    },
  })
}

/**
 * @description 创建Demo
 * @param {DemoCreate} data 创建数据
 * @returns {Promise<Demo>} 返回创建的Demo信息
 */
const createDemo = (data: DemoCreate) => {
  return alovaInstance.Post<Demo>('/v1/demo/demo/create', data)
}

/**
 * @description 更新Demo
 * @param {DemoUpdate} data 更新数据
 * @returns {Promise<Demo>} 返回更新后的Demo信息
 */
const updateDemo = (data: DemoUpdate) => {
  return alovaInstance.Put<Demo>('/v1/demo/demo/update', data)
}

/**
 * @description 删除Demo
 * @param {number} id Demo ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeDemo = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/demo/demo/delete/${id}`)
}

/**
 * @description 批量删除Demo
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteDemo = (ids: number[]) => {
  return alovaInstance.Delete<any>('/v1/demo/demo/bulk_delete', ids)
}

export {
  getDemoMetadata,
  getDemoList,
  getDemo,
  createDemo,
  updateDemo,
  removeDemo,
  bulkDeleteDemo,
  // exportDemo,
  // importDemo,
}
