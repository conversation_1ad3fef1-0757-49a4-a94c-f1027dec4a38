# 关系列增强功能使用指南

## 概述

关系列增强功能为 DataGrid 中的关系字段提供了强大的详情展示能力，支持三种展示方式：
1. **业务组件展示** - 动态加载指定的 Vue 组件
2. **路由页面展示** - 在 drawer/dialog 中展示指定路由页面
3. **默认数据预览** - 智能展示关系数据的键值对信息

## 配置选项

### 基础配置
```typescript
interface RelationRendererConfig {
  // 详情展示模式
  detailMode?: 'drawer' | 'dialog' | 'page'
  
  // 业务组件配置
  component?: string | any // 组件名称或组件对象
  componentProps?: Record<string, any> // 传递给组件的 props
  
  // 路由配置
  routePath?: string | ((data: any) => string) // 路由路径
  routeQuery?: Record<string, any> | ((data: any) => Record<string, any>) // 路由查询参数
  
  // 弹窗配置
  title?: string | ((data: any) => string) // 弹窗标题
  drawerSize?: 'large' | 'middle' | 'small' | 'mini' // drawer 尺寸
  dialogSize?: 'large' | 'middle' | 'small' | 'mini' // dialog 尺寸
  
  // 事件回调
  onOpen?: (data: any) => void // 打开时回调
  onClose?: () => void // 关闭时回调
}
```

## 使用示例

### 1. 使用业务组件展示（Drawer）

```typescript
columnHelper.relation('sales', '销售员', {
  displayField: 'nick_name',
  secondaryFields: ['employee_no'],
  variant: 'link',
  detailMode: 'drawer',
  component: 'business/SalesPersonDetail', // 组件路径
  title: (data: any) => `销售员详情 - ${data?.nick_name || '未知'}`,
  drawerSize: 'large',
  componentProps: {
    editable: true, // 传递给组件的额外属性
    showActions: true,
  },
  onOpen: (data: any) => {
    console.log('打开销售员详情:', data)
  },
  onClose: () => {
    console.log('关闭销售员详情')
  },
})
```

### 2. 使用对话框展示

```typescript
columnHelper.relation('product', '产品', {
  displayField: 'product_name',
  variant: 'badge',
  detailMode: 'dialog',
  component: 'business/ProductDetail',
  title: '产品详情',
  dialogSize: 'middle',
})
```

### 3. 使用路由页面展示

```typescript
columnHelper.relation('customer', '客户', {
  displayField: 'customer_name',
  variant: 'link',
  detailMode: 'page',
  routePath: (data: any) => `/customer/detail/${data?.id}`,
  routeQuery: (data: any) => ({
    tab: 'overview',
    from: 'datagrid'
  }),
  onOpen: (data: any) => {
    console.log('跳转到客户详情页面:', data)
  },
})
```

### 4. 在 drawer 中展示路由页面

```typescript
columnHelper.relation('order', '订单', {
  displayField: 'order_no',
  variant: 'link',
  detailMode: 'drawer',
  routePath: '/order/detail',
  routeQuery: (data: any) => ({ id: data.id }),
  title: (data: any) => `订单详情 - ${data.order_no}`,
  drawerSize: 'large',
})
```

## 业务组件开发指南

### 组件接口

业务组件会接收以下 props：

```typescript
interface BusinessComponentProps {
  data: any // 关系数据
  row: any // 当前行数据
  // ...其他通过 componentProps 传递的属性
}
```

### 示例业务组件

```vue
<template>
  <div class="business-detail">
    <h2>{{ data?.name || '详情' }}</h2>
    <div class="content">
      <!-- 业务逻辑展示 -->
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any
  row?: any
  editable?: boolean
}

const props = defineProps<Props>()

// 业务逻辑
</script>
```

## 组件放置位置

业务组件应放置在 `src/components/` 目录下，支持子目录结构：

```
src/components/
├── business/
│   ├── SalesPersonDetail.vue
│   ├── ProductDetail.vue
│   └── CustomerDetail.vue
└── common/
    └── ...
```

## 路由配置

### 动态路由路径

```typescript
routePath: (data: any) => `/detail/${data.type}/${data.id}`
```

### 查询参数

```typescript
routeQuery: (data: any) => ({
  id: data.id,
  tab: 'overview',
  mode: 'view'
})
```

## 事件处理

### 打开事件

```typescript
onOpen: (data: any) => {
  // 记录用户行为
  analytics.track('relation_detail_opened', {
    type: 'sales',
    id: data.id
  })
}
```

### 关闭事件

```typescript
onClose: () => {
  // 清理资源或保存状态
  console.log('详情窗口已关闭')
}
```

## 最佳实践

1. **组件命名**：使用描述性的组件名称，如 `SalesPersonDetail`、`ProductDetail`
2. **尺寸选择**：根据内容复杂度选择合适的尺寸
   - `mini`: 简单信息展示
   - `small`: 基础详情
   - `middle`: 标准详情（推荐）
   - `large`: 复杂详情或表单
3. **错误处理**：在业务组件中处理数据为空或异常的情况
4. **性能优化**：使用动态导入避免不必要的组件加载
5. **用户体验**：提供加载状态和错误提示

## 故障排除

### 组件无法加载
- 检查组件路径是否正确
- 确认组件文件存在
- 查看控制台错误信息

### 路由跳转失败
- 检查路由配置是否正确
- 确认目标路由存在
- 验证路由参数格式

### 数据传递问题
- 检查 `componentProps` 配置
- 确认业务组件的 props 定义
- 使用浏览器开发工具调试
