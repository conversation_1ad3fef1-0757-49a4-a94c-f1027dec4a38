<template>
  <div ref="containerRef" class="relation-renderer">
    <!-- 无数据情况 -->
    <span v-if="!hasData" class="text-gray-400">--</span>

    <!-- 多对一：单个对象显示 -->
    <template v-else-if="relationType === 'MANYTOONE'">
      <!-- Badge 变体 -->
      <Badge
        v-if="finalConfig.variant === 'badge'"
        class="cursor-pointer hover:opacity-80 transition-opacity justify-start"
        @click="handleClick(relationData)"
      >
        {{ singleItemText }}
      </Badge>

      <!-- Link 变体 -->
      <span
        v-else-if="finalConfig.variant === 'link'"
        :class="variantClasses"
        @click="handleClick(relationData)"
      >
        {{ singleItemText }}
      </span>

      <!-- Avatar 变体 -->
      <div
        v-else-if="finalConfig.variant === 'avatar'"
        :class="variantClasses"
        @click="handleClick(relationData)"
      >
        <Avatar class="w-6 h-6">
          <AvatarFallback class="text-xs">
            {{ getAvatarText(singleItemText) }}
          </AvatarFallback>
        </Avatar>
        <span class="ml-2 cursor-pointer">{{ singleItemText }}</span>
      </div>

      <!-- Text 变体（默认） -->
      <span
        v-else
        :class="variantClasses"
        @click="finalConfig.onClick ? handleClick(relationData) : undefined"
        :style="{ cursor: finalConfig.onClick ? 'pointer' : 'default' }"
      >
        {{ singleItemText }}
        <!-- 次要信息 -->
        <span
          v-if="secondaryInfo.length > 0"
          class="text-gray-500 text-xs ml-1"
        >
          ({{ secondaryInfo.join(', ') }})
        </span>
      </span>
    </template>

    <!-- 一对多：列表/计数显示 -->
    <template v-else-if="relationType === 'ONETOMANY'">
      <!-- 计数模式 -->
      <div v-if="finalConfig.overflowMode === 'count'">
        <!-- 计数器 -->
        <Badge
          variant="outline"
          class="cursor-pointer justify-start hover:bg-gray-50"
          @click="handleCounterClick"
        >
          {{ relationData.length }}个项目
          <span class="ml-1 text-xs">{{ isExpanded ? '▼' : '▶' }}</span>
        </Badge>

        <!-- 优化：展开内容使用更简单的渲染，减少Badge组件数量 -->
        <div v-if="isExpanded" class="mt-2">
          <div class="flex items-center gap-1 flex-wrap justify-start text-sm">
            <span
              v-for="(item, index) in relationData"
              :key="item.id || index"
              class="px-2 py-1 bg-gray-100 rounded cursor-pointer hover:bg-gray-200 transition-colors"
              @click="handleItemClick(item)"
            >
              {{ getItemDisplayText(item) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 展开模式或提示模式 -->
      <div v-else class="flex items-center gap-1 flex-wrap justify-start">
        <!-- 显示的项目 -->
        <Badge
          v-for="(item, index) in isExpandModeExpanded
            ? relationData
            : displayItems"
          :key="item.id || index"
          variant="secondary"
          class="cursor-pointer hover:opacity-80 transition-opacity justify-start"
          @click="handleItemClick(item)"
        >
          {{ getItemDisplayText(item) }}
        </Badge>

        <!-- 更多项目提示 -->
        <template v-if="hasMore && !isExpandModeExpanded">
          <Tooltip v-if="finalConfig.overflowMode === 'tooltip'">
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                class="cursor-pointer justify-start"
                @click="handleClick(relationData)"
              >
                +{{ remainingCount }}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div class="max-w-xs">
                <div class="font-medium mb-1">更多项目:</div>
                <div class="space-y-1">
                  <div
                    v-for="(item, index) in relationData.slice(
                      finalConfig.maxDisplay
                    )"
                    :key="item.id || index"
                    class="text-sm"
                  >
                    {{ getItemDisplayText(item) }}
                  </div>
                </div>
                <div class="text-xs text-gray-500 mt-2">点击查看全部</div>
              </div>
            </TooltipContent>
          </Tooltip>

          <!-- expand 模式：点击展开更多项目 -->
          <Badge
            v-else-if="finalConfig.overflowMode === 'expand'"
            variant="outline"
            class="cursor-pointer justify-start hover:bg-gray-50"
            @click="handleExpandModeToggle"
          >
            +{{ remainingCount }}
          </Badge>
        </template>

        <!-- 收起按钮（当展开时显示） -->
        <Badge
          v-if="isExpandModeExpanded"
          variant="outline"
          class="cursor-pointer justify-start hover:bg-gray-50"
          @click="handleExpandModeToggle"
        >
          收起 ▲
        </Badge>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'RelationRenderer',
})

import { ref, nextTick, onBeforeUnmount } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import type { RelationRendererProps } from './types'
import { useRelationRenderer } from './useRenderer'
import {
  adjustRowHeight,
  resetRowHeight,
} from '@/components/data-grid/utils/rowHeightHelpers'

const props = defineProps<RelationRendererProps>()

// 容器引用，用于行高调整
const containerRef = ref<HTMLElement | null>(null)
const isDestroying = ref(false)

// 本地状态：控制计数器是否展开（count 模式）
const isExpanded = ref(false)

// 本地状态：控制 expand 模式是否展开更多项目
const isExpandModeExpanded = ref(false)

const {
  relationData,
  relationType,
  displayText,
  singleItemText,
  displayItems,
  secondaryInfo,
  hasData,
  hasMore,
  remainingCount,
  finalConfig,
  handleClick,
  getItemDisplayText,
  variantClasses,
} = useRelationRenderer(props)

/**
 * 获取头像显示文本（取首字符）
 */
const getAvatarText = (text: string): string => {
  return text.charAt(0).toUpperCase()
}

/**
 * 处理计数器点击 - 展开/折叠内容（count 模式）
 */
const handleCounterClick = () => {
  isExpanded.value = !isExpanded.value

  // 使用 nextTick 确保 DOM 更新完成后再调整行高
  nextTick(() => {
    if (isDestroying.value) return
    if (isExpanded.value) {
      // 展开时调整行高以适应内容
      adjustRowHeight(containerRef.value, {
        minHeight: 40,
        padding: 12,
        addMarkerClass: true,
      })
    } else {
      // 收起时重置行高
      resetRowHeight(containerRef.value)
    }
  })
}

/**
 * 处理 expand 模式的展开/收起切换
 */
const handleExpandModeToggle = () => {
  isExpandModeExpanded.value = !isExpandModeExpanded.value

  // 使用 nextTick 确保 DOM 更新完成后再调整行高
  nextTick(() => {
    if (isDestroying.value) return
    if (isExpandModeExpanded.value) {
      // 展开时调整行高以适应更多内容
      adjustRowHeight(containerRef.value, {
        minHeight: 40,
        padding: 12,
        addMarkerClass: true,
      })
    } else {
      // 收起时重置行高
      resetRowHeight(containerRef.value)
    }
  })
}

/**
 * 处理项目点击 - 调用原有的点击处理
 */
const handleItemClick = (item: any) => {
  handleClick(item)
}

// 组件卸载前清理
onBeforeUnmount(() => {
  isDestroying.value = true
})
</script>

<style scoped>
.relation-renderer {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
}

.relation-renderer .badge + .badge {
  margin-left: 0.25rem;
}
</style>
