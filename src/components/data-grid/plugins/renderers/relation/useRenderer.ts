/**
 * 关系渲染器业务逻辑钩子
 *
 * 提供关系数据处理、配置合并、交互处理等核心逻辑
 */

import { computed } from 'vue'
import type { RelationRendererProps } from './types'
import {
  formatRelationDisplay,
  getRelationType,
  inferDisplayField,
  buildSecondaryInfo,
} from './utils'

export const useRelationRenderer = (props: RelationRendererProps) => {
  // 获取关系数据 - 保持与其他渲染器一致的逻辑
  const relationData = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // 关系类型判断
  const relationType = computed(() => {
    return getRelationType(relationData.value)
  })

  // 配置合并 - 提供合理的默认值
  const finalConfig = computed(() => ({
    variant: 'text' as const,
    autoFromMetadata: true,
    maxDisplay: 2,
    overflowMode: 'tooltip' as const,
    detailMode: 'drawer' as const,
    errorText: '--',
    ...props.config,
  }))

  // 显示字段推断
  const displayField = computed(() => {
    if (finalConfig.value.displayField) {
      return finalConfig.value.displayField
    }

    // 自动推断显示字段
    if (finalConfig.value.autoFromMetadata && relationData.value) {
      const sampleData = Array.isArray(relationData.value)
        ? relationData.value[0]
        : relationData.value
      return inferDisplayField(sampleData)
    }

    return null
  })

  // 主显示文本
  const displayText = computed(() => {
    try {
      return formatRelationDisplay(relationData.value, finalConfig.value)
    } catch (error) {
      console.error('关系渲染器显示文本格式化失败:', error)
      return finalConfig.value.errorText || '--'
    }
  })

  // 是否有数据
  const hasData = computed(() => {
    if (Array.isArray(relationData.value)) {
      return relationData.value.length > 0
    }
    return relationData.value != null
  })

  // 是否有更多项目（用于一对多溢出显示）
  const hasMore = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return relationData.value.length > maxDisplay
    }
    return false
  })

  // 剩余项目数量
  const remainingCount = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return Math.max(0, relationData.value.length - maxDisplay)
    }
    return 0
  })

  // 显示的项目列表（用于一对多）
  const displayItems = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return relationData.value.slice(0, maxDisplay)
    }
    return []
  })

  // 单个项目显示文本（用于多对一）
  const singleItemText = computed(() => {
    if (relationType.value === 'MANYTOONE' && relationData.value) {
      return getItemDisplayText(relationData.value)
    }
    return '--'
  })

  // 次要信息（如员工编号等）
  const secondaryInfo = computed(() => {
    const data = relationType.value === 'MANYTOONE' ? relationData.value : null

    if (data && finalConfig.value.secondaryFields) {
      return buildSecondaryInfo(data, finalConfig.value.secondaryFields)
    }
    return []
  })

  // 获取单个项目的显示文本
  const getItemDisplayText = (item: any): string => {
    if (!item || typeof item !== 'object') {
      return String(item || '--')
    }

    const field = displayField.value
    if (field && item[field]) {
      return String(item[field])
    }

    // 使用推断的显示字段
    const inferred = inferDisplayField(item)
    if (inferred && item[inferred]) {
      return String(item[inferred])
    }

    return item.id ? `项目 (ID: ${item.id})` : '未知项目'
  }

  // 点击处理
  const handleClick = (data: any) => {
    console.log('关系列点击事件触发:', {
      data,
      config: finalConfig.value,
      hasModuleModel: !!finalConfig.value.moduleModel,
      hasComponent: !!finalConfig.value.component,
      hasRoutePath: !!finalConfig.value.routePath,
    })

    if (finalConfig.value.onClick) {
      const result = finalConfig.value.onClick(data, props.row)
      // 如果返回 false，阻止默认行为
      if (result === false) {
        return
      }
    }

    // 默认点击行为：打开详情
    openDetail(data)
  }

  // 打开详情 - 集成API服务和弹窗系统
  const openDetail = async (data: any) => {
    // 如果是数组，显示列表；如果是对象，显示详情
    if (Array.isArray(data)) {
      openListDetail(data)
    } else {
      openSingleDetail(data)
    }
  }

  // 打开单项详情
  const openSingleDetail = async (item: any) => {
    console.log('准备打开单项详情:', {
      item,
      detailMode: finalConfig.value.detailMode,
      moduleModel: finalConfig.value.moduleModel,
      component: finalConfig.value.component,
    })

    if (!item) {
      console.warn('无法打开详情：缺少有效的项目数据')
      return
    }

    try {
      // 根据配置选择详情模式
      const mode = finalConfig.value.detailMode || 'drawer'
      const config = finalConfig.value

      console.log('使用详情模式:', mode)

      if (mode === 'drawer') {
        // 使用抽屉显示详情
        await openDrawerDetail(item, config)
      } else if (mode === 'dialog') {
        // 使用对话框显示详情
        await openDialogDetail(item, config)
      } else if (mode === 'page') {
        // 跳转到详情页面
        await openPageDetail(item, config)
      }
    } catch (error) {
      console.error('打开详情失败:', error)
    }
  }

  // 打开列表详情
  const openListDetail = async (items: any[]) => {
    console.log('打开列表详情:', items)
    // 对于列表数据，使用相同的详情展示逻辑
    await openSingleDetail(items)
  }

  // 使用抽屉显示详情
  const openDrawerDetail = async (data: any, config: any) => {
    console.log('准备打开抽屉详情:', {
      data,
      config,
      hasModuleModel: !!config.moduleModel,
    })

    // 使用自定义抽屉实现
    await openCustomDrawer(data, config)
  }

  // 自定义抽屉实现
  const openCustomDrawer = async (data: any, config: any) => {
    const { createApp, h, ref } = await import('vue')

    // 动态导入所需组件
    const [SideDrawer, RelationDetailViewer] = await Promise.all([
      import('@/components/common/SideDrawer.vue').then((m) => m.default),
      import('./RelationDetailViewer.vue').then((m) => m.default),
    ])

    const title =
      typeof config.title === 'function'
        ? config.title(data)
        : config.title || '关联详情'

    console.log('创建自定义抽屉:', {
      title,
      componentProps: {
        relationData: data,
        row: props.row,
        config,
      },
    })

    // 创建抽屉容器
    const drawerContainer = document.createElement('div')
    document.body.appendChild(drawerContainer)

    // 创建 Vue 应用实例
    const app = createApp({
      setup() {
        const visible = ref(true)

        const handleClose = () => {
          visible.value = false
          // 延迟销毁，等待动画完成
          setTimeout(() => {
            app.unmount()
            document.body.removeChild(drawerContainer)
            if (config.onClose) {
              config.onClose()
            }
          }, 300)
        }

        return {
          visible,
          handleClose,
          data,
          config,
          row: props.row,
          title,
        }
      },
      render() {
        return h(
          SideDrawer,
          {
            modelValue: this.visible,
            'onUpdate:modelValue': this.handleClose,
            contentClass: this.getSizeClass(config.drawerSize || 'middle'),
          },
          {
            header: () =>
              h('div', { class: 'p-4 border-b' }, [
                h('h2', { class: 'text-lg font-semibold' }, this.title),
              ]),
            default: () =>
              h(RelationDetailViewer, {
                relationData: this.data,
                row: this.row,
                config: this.config,
              }),
          }
        )
      },
      methods: {
        getSizeClass(size: string) {
          const sizeMap = {
            mini: 'w-80',
            small: 'w-96',
            middle: 'w-[600px]',
            large: 'w-[800px]',
          }
          return sizeMap[size] || sizeMap.middle
        },
      },
    })

    app.mount(drawerContainer)
  }

  // 使用对话框显示详情
  const openDialogDetail = async (data: any, config: any) => {
    console.log('准备打开对话框详情:', {
      data,
      config,
      hasModuleModel: !!config.moduleModel,
    })

    // 使用自定义对话框实现
    await openCustomDialog(data, config)
  }

  // 自定义对话框实现
  const openCustomDialog = async (data: any, config: any) => {
    const { createApp, h, ref } = await import('vue')

    // 动态导入所需组件
    const [
      Dialog,
      DialogContent,
      DialogHeader,
      DialogTitle,
      RelationDetailViewer,
    ] = await Promise.all([
      import('@/components/ui/dialog').then((m) => m.Dialog),
      import('@/components/ui/dialog').then((m) => m.DialogContent),
      import('@/components/ui/dialog').then((m) => m.DialogHeader),
      import('@/components/ui/dialog').then((m) => m.DialogTitle),
      import('./RelationDetailViewer.vue').then((m) => m.default),
    ])

    const title =
      typeof config.title === 'function'
        ? config.title(data)
        : config.title || '关联详情'

    console.log('创建自定义对话框:', {
      title,
      componentProps: {
        relationData: data,
        row: props.row,
        config,
      },
    })

    // 创建对话框容器
    const dialogContainer = document.createElement('div')
    document.body.appendChild(dialogContainer)

    // 创建 Vue 应用实例
    const app = createApp({
      setup() {
        const visible = ref(true)

        const handleClose = () => {
          visible.value = false
          // 延迟销毁，等待动画完成
          setTimeout(() => {
            app.unmount()
            document.body.removeChild(dialogContainer)
            if (config.onClose) {
              config.onClose()
            }
          }, 300)
        }

        return {
          visible,
          handleClose,
          data,
          config,
          row: props.row,
          title,
        }
      },
      render() {
        return h(
          Dialog,
          {
            open: this.visible,
            'onUpdate:open': this.handleClose,
          },
          {
            default: () =>
              h(
                DialogContent,
                {
                  class: this.getSizeClass(config.dialogSize || 'middle'),
                },
                {
                  default: () => [
                    h(
                      DialogHeader,
                      {},
                      {
                        default: () => h(DialogTitle, {}, this.title),
                      }
                    ),
                    h('div', { class: 'max-h-[80vh] overflow-y-auto p-4' }, [
                      h(RelationDetailViewer, {
                        relationData: this.data,
                        row: this.row,
                        config: this.config,
                      }),
                    ]),
                  ],
                }
              ),
          }
        )
      },
      methods: {
        getSizeClass(size: string) {
          const sizeMap = {
            mini: 'w-[30%]',
            small: 'w-[50%]',
            middle: 'w-[70%]',
            large: 'w-[90%]',
          }
          return sizeMap[size] || sizeMap.middle
        },
      },
    })

    app.mount(dialogContainer)
  }

  // 跳转到详情页面
  const openPageDetail = async (data: any, config: any) => {
    if (!config.routePath) {
      console.warn('页面模式需要配置 routePath')
      return
    }

    const { useRouter } = await import('vue-router')
    const router = useRouter()

    let path = ''
    if (typeof config.routePath === 'function') {
      path = config.routePath(data)
    } else {
      path = config.routePath
    }

    // 构建路由参数
    const routeParams: any = { path }

    if (config.routeQuery) {
      const query =
        typeof config.routeQuery === 'function'
          ? config.routeQuery(data)
          : config.routeQuery
      routeParams.query = query
    }

    // 触发打开回调
    if (config.onOpen) {
      config.onOpen(data)
    }

    router.push(routeParams)
  }

  // 样式相关的计算属性
  const variantClasses = computed(() => {
    const variant = finalConfig.value.variant

    switch (variant) {
      case 'badge':
        return 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
      case 'link':
        return 'text-blue-600 hover:text-blue-800 cursor-pointer underline'
      case 'avatar':
        return 'flex items-center space-x-2'
      case 'text':
      default:
        return 'text-gray-900'
    }
  })

  return {
    // 数据
    relationData,
    relationType,
    displayText,
    singleItemText,
    displayItems,
    secondaryInfo,

    // 状态
    hasData,
    hasMore,
    remainingCount,

    // 配置
    finalConfig,
    displayField,

    // 方法
    handleClick,
    getItemDisplayText,

    // 样式
    variantClasses,
  }
}
