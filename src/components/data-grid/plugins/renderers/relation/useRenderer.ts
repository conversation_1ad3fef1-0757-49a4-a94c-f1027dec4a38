/**
 * 关系渲染器业务逻辑钩子
 *
 * 提供关系数据处理、配置合并、交互处理等核心逻辑
 */

import { computed } from 'vue'
import type { RelationRendererProps } from './types'
import {
  formatRelationDisplay,
  getRelationType,
  inferDisplayField,
  buildSecondaryInfo,
} from './utils'

export const useRelationRenderer = (props: RelationRendererProps) => {
  // 获取关系数据 - 保持与其他渲染器一致的逻辑
  const relationData = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // 关系类型判断
  const relationType = computed(() => {
    return getRelationType(relationData.value)
  })

  // 配置合并 - 提供合理的默认值
  const finalConfig = computed(() => ({
    variant: 'text' as const,
    autoFromMetadata: true,
    maxDisplay: 2,
    overflowMode: 'tooltip' as const,
    detailMode: 'drawer' as const,
    errorText: '--',
    ...props.config,
  }))

  // 显示字段推断
  const displayField = computed(() => {
    if (finalConfig.value.displayField) {
      return finalConfig.value.displayField
    }

    // 自动推断显示字段
    if (finalConfig.value.autoFromMetadata && relationData.value) {
      const sampleData = Array.isArray(relationData.value)
        ? relationData.value[0]
        : relationData.value
      return inferDisplayField(sampleData)
    }

    return null
  })

  // 主显示文本
  const displayText = computed(() => {
    try {
      return formatRelationDisplay(relationData.value, finalConfig.value)
    } catch (error) {
      console.error('关系渲染器显示文本格式化失败:', error)
      return finalConfig.value.errorText || '--'
    }
  })

  // 是否有数据
  const hasData = computed(() => {
    if (Array.isArray(relationData.value)) {
      return relationData.value.length > 0
    }
    return relationData.value != null
  })

  // 是否有更多项目（用于一对多溢出显示）
  const hasMore = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return relationData.value.length > maxDisplay
    }
    return false
  })

  // 剩余项目数量
  const remainingCount = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return Math.max(0, relationData.value.length - maxDisplay)
    }
    return 0
  })

  // 显示的项目列表（用于一对多）
  const displayItems = computed(() => {
    if (
      relationType.value === 'ONETOMANY' &&
      Array.isArray(relationData.value)
    ) {
      const maxDisplay = finalConfig.value.maxDisplay || 2
      return relationData.value.slice(0, maxDisplay)
    }
    return []
  })

  // 单个项目显示文本（用于多对一）
  const singleItemText = computed(() => {
    if (relationType.value === 'MANYTOONE' && relationData.value) {
      return getItemDisplayText(relationData.value)
    }
    return '--'
  })

  // 次要信息（如员工编号等）
  const secondaryInfo = computed(() => {
    const data = relationType.value === 'MANYTOONE' ? relationData.value : null

    if (data && finalConfig.value.secondaryFields) {
      return buildSecondaryInfo(data, finalConfig.value.secondaryFields)
    }
    return []
  })

  // 获取单个项目的显示文本
  const getItemDisplayText = (item: any): string => {
    if (!item || typeof item !== 'object') {
      return String(item || '--')
    }

    const field = displayField.value
    if (field && item[field]) {
      return String(item[field])
    }

    // 使用推断的显示字段
    const inferred = inferDisplayField(item)
    if (inferred && item[inferred]) {
      return String(item[inferred])
    }

    return item.id ? `项目 (ID: ${item.id})` : '未知项目'
  }

  // 点击处理
  const handleClick = (data: any) => {
    if (finalConfig.value.onClick) {
      const result = finalConfig.value.onClick(data, props.row)
      // 如果返回 false，阻止默认行为
      if (result === false) {
        return
      }
    }

    // 默认点击行为：打开详情
    openDetail(data)
  }

  // 打开详情 - 集成API服务和弹窗系统
  const openDetail = async (data: any) => {
    // 如果是数组，显示列表；如果是对象，显示详情
    if (Array.isArray(data)) {
      openListDetail(data)
    } else {
      openSingleDetail(data)
    }
  }

  // 打开单项详情
  const openSingleDetail = async (item: any) => {
    if (!item || !item.id) {
      console.warn('无法打开详情：缺少有效的项目数据')
      return
    }

    try {
      // 根据配置选择详情模式
      const mode = finalConfig.value.detailMode

      if (mode === 'drawer') {
        // 使用抽屉显示详情
        const { useDrawerFormStore } = await import('@/store/drawerForm')
        const drawerFormStore = useDrawerFormStore()
        const drawerApi = drawerFormStore.getDrawerFormApi()

        // TODO: 这里需要根据关系模型获取对应的表单配置
        console.log('使用抽屉显示详情:', item)
        console.log('抽屉API:', drawerApi)
      } else if (mode === 'modal') {
        // 使用模态框显示详情
        console.log('使用模态框显示详情:', item)
      } else if (mode === 'page') {
        // 跳转到详情页面
        console.log('跳转到详情页面:', item)
      }
    } catch (error) {
      console.error('打开详情失败:', error)
    }
  }

  // 打开列表详情
  const openListDetail = async (items: any[]) => {
    console.log('显示关系列表:', items)
    // TODO: 实现关系列表显示逻辑
  }

  // 样式相关的计算属性
  const variantClasses = computed(() => {
    const variant = finalConfig.value.variant

    switch (variant) {
      case 'badge':
        return 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
      case 'link':
        return 'text-blue-600 hover:text-blue-800 cursor-pointer underline'
      case 'avatar':
        return 'flex items-center space-x-2'
      case 'text':
      default:
        return 'text-gray-900'
    }
  })

  return {
    // 数据
    relationData,
    relationType,
    displayText,
    singleItemText,
    displayItems,
    secondaryInfo,

    // 状态
    hasData,
    hasMore,
    remainingCount,

    // 配置
    finalConfig,
    displayField,

    // 方法
    handleClick,
    getItemDisplayText,

    // 样式
    variantClasses,
  }
}
