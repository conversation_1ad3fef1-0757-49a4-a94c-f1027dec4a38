/**
 * 关系渲染器类型定义
 *
 * 用于定义关系字段渲染相关的配置和接口
 * 支持多对一、一对多等关系类型的渲染配置
 */

import type { BaseRendererConfig, BaseRendererProps } from '../../types'

// 关联模型映射类型
export type RelationModelMapping = Record<string, string>

export interface RelationRendererConfig extends BaseRendererConfig {
  // 基础配置
  autoFromMetadata?: boolean
  displayField?: string
  secondaryFields?: string[]
  formatter?: (data: any, row?: any) => string

  // 显示配置
  variant?: 'text' | 'badge' | 'avatar' | 'link'
  maxDisplay?: number
  overflowMode?: 'tooltip' | 'expand' | 'count'

  // 交互配置
  onClick?: (relationData: any, row: any) => void | boolean
  detailMode?: 'modal' | 'drawer' | 'page'

  // 错误处理
  errorText?: string
}

export interface RelationRendererProps extends BaseRendererProps {
  config?: RelationRendererConfig
}