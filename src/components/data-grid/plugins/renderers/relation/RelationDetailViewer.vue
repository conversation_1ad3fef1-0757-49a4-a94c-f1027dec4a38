<template>
  <div class="relation-detail-viewer">
    <!-- 动态组件展示 -->
    <component
      v-if="config.component && !config.routePath"
      :is="resolvedComponent"
      v-bind="componentProps"
      :data="relationData"
      :row="row"
    />

    <!-- 路由页面展示 -->
    <div v-else-if="config.routePath" class="route-container">
      <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center space-x-2 mb-3">
          <svg
            class="w-5 h-5 text-blue-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7l-10 10"
            ></path>
          </svg>
          <h3 class="text-lg font-medium text-blue-800">页面跳转</h3>
        </div>
        <p class="text-blue-700 mb-4">点击下方按钮将在新页面中打开详情内容</p>
        <div class="flex space-x-2">
          <button
            @click="openInNewTab"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            在新标签页中打开
          </button>
          <button
            @click="openInCurrentPage"
            class="px-4 py-2 border border-blue-600 text-blue-600 rounded hover:bg-blue-50 transition-colors"
          >
            在当前页面打开
          </button>
        </div>
        <div class="mt-3 text-sm text-blue-600">
          <strong>目标地址:</strong> {{ getRouteUrl() }}
        </div>
      </div>
    </div>

    <!-- 默认数据预览 -->
    <div v-else class="default-preview p-4">
      <div v-if="Array.isArray(relationData)" class="array-data">
        <h3 class="text-lg font-semibold mb-3">关联数据列表</h3>
        <div class="space-y-2">
          <div
            v-for="(item, index) in relationData"
            :key="index"
            class="p-3 border rounded-lg bg-gray-50"
          >
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div v-for="(value, key) in getDisplayFields(item)" :key="key">
                <span class="font-medium text-gray-600">{{ key }}:</span>
                <span class="ml-1">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-else-if="relationData && typeof relationData === 'object'"
        class="object-data"
      >
        <h3 class="text-lg font-semibold mb-3">关联数据详情</h3>
        <div class="grid grid-cols-1 gap-3">
          <div
            v-for="(value, key) in getDisplayFields(relationData)"
            :key="key"
            class="flex justify-between py-2 border-b border-gray-200"
          >
            <span class="font-medium text-gray-600">{{ key }}:</span>
            <span class="text-right">{{ value }}</span>
          </div>
        </div>
      </div>

      <div v-else class="empty-data">
        <p class="text-gray-500 text-center py-8">暂无关联数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, defineAsyncComponent } from 'vue'
import type { RelationRendererConfig } from './types'

interface Props {
  relationData: any
  row: any
  config: RelationRendererConfig
}

const props = defineProps<Props>()

// 解析组件
const resolvedComponent = computed(() => {
  if (!props.config.component) return null

  if (typeof props.config.component === 'string') {
    // 如果是字符串，尝试动态导入组件
    try {
      return defineAsyncComponent(
        () => import(`@/components/${props.config.component}.vue`)
      )
    } catch (error) {
      console.warn(`无法加载组件: ${props.config.component}`, error)
      return null
    }
  }

  // 如果是组件对象，直接返回
  return props.config.component
})

// 组件属性
const componentProps = computed(() => ({
  ...props.config.componentProps,
  data: props.relationData,
  row: props.row,
}))

// 获取路由 URL
const getRouteUrl = () => {
  if (!props.config.routePath) return ''

  let path = ''
  if (typeof props.config.routePath === 'function') {
    path = props.config.routePath(props.relationData)
  } else {
    path = props.config.routePath
  }

  // 构建查询参数
  let query = ''
  if (props.config.routeQuery) {
    const queryParams =
      typeof props.config.routeQuery === 'function'
        ? props.config.routeQuery(props.relationData)
        : props.config.routeQuery

    const searchParams = new URLSearchParams()
    Object.entries(queryParams).forEach(([key, value]) => {
      searchParams.append(key, String(value))
    })
    query = searchParams.toString()
  }

  return query ? `${path}?${query}` : path
}

// 在新标签页中打开
const openInNewTab = () => {
  const url = getRouteUrl()
  if (url) {
    window.open(url, '_blank')
  }
}

// 在当前页面打开
const openInCurrentPage = async () => {
  const url = getRouteUrl()
  if (url) {
    const { useRouter } = await import('vue-router')
    const router = useRouter()

    // 解析 URL
    const [path, queryString] = url.split('?')
    const query: Record<string, string> = {}

    if (queryString) {
      const searchParams = new URLSearchParams(queryString)
      searchParams.forEach((value, key) => {
        query[key] = value
      })
    }

    router.push({ path, query })
  }
}

// 获取要显示的字段
const getDisplayFields = (data: any) => {
  if (!data || typeof data !== 'object') return {}

  // 过滤掉一些不需要显示的字段
  const excludeFields = [
    'created_at',
    'updated_at',
    'deleted_at',
    'password',
    'token',
  ]
  const result: Record<string, any> = {}

  Object.entries(data).forEach(([key, value]) => {
    if (!excludeFields.includes(key) && value !== null && value !== undefined) {
      // 格式化字段名
      const displayKey = key
        .replace(/_/g, ' ')
        .replace(/\b\w/g, (l) => l.toUpperCase())
      result[displayKey] = value
    }
  })

  return result
}

onMounted(() => {
  // 触发打开回调
  if (props.config.onOpen) {
    props.config.onOpen(props.relationData)
  }
})
</script>

<style scoped>
.relation-detail-viewer {
  min-height: 200px;
}

.route-container {
  height: 500px;
  width: 100%;
}

.default-preview {
  max-height: 400px;
  overflow-y: auto;
}

.array-data .space-y-2 > * + * {
  margin-top: 0.5rem;
}
</style>
